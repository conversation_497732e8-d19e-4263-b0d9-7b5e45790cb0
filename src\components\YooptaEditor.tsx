"use client";

import YooptaEditor, {
  createYooptaEditor,
  YooptaContentValue,
  YooptaOnChangeOptions,
} from "@yoopta/editor";
import Paragraph from "@yoopta/paragraph";
import Blockquote from "@yoopta/blockquote";
import Headings from "@yoopta/headings";
import { useMemo, useState } from "react";
import {
  Bold,
  Italic,
  CodeMark,
  Underline,
  Strike,
  Highlight,
} from "@yoopta/marks";

const PLUGINS = [Paragraph, Blockquote, Headings];
const MARKS = [Bold, Italic, CodeMark, Underline, Strike, Highlight];

export interface YooptaEditorProps {
  editor: any;
  placeholder: string;
  value: YooptaContentValue;
  onChange: (value: YooptaContentValue, options: YooptaOnChangeOptions) => void;
}

export default function YooptaEditor() {
  const editor = useMemo(() => createYooptaEditor(), []);
  const [value, setValue] = useState<YooptaContentValue>();
  const onChange = (
    value: YooptaContentValue,
    options: YooptaOnChangeOptions
  ) => {
    setValue(value);
  };

  return (
    <div>
      <YooptaEditor
        editor={editor}
        placeholder="Type text.."
        value={value}
        onChange={onChange}
        plugins={PLUGINS}
        marks={MARKS}
      />
    </div>
  );
}
